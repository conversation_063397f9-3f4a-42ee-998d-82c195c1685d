<template>
  <div>
    <div class="p-6 rounded-lg border bg-card text-card-foreground shadow-sm">
      <div class="mb-2 flex justify-between items-center">
        <h3 class="text-lg font-semibold">预测进度</h3>
        <span>{{ taskProgress }}%</span>
      </div>
      <Progress v-model="taskProgress" :min="0" :max="100" class="w-full" />
    </div>
    <div class="bg-card text-card-foreground shadow-sm mt-4">
      <!-- <div class="flex justify-between items-center">
        <h3 class="text-lg font-semibold">预览设置</h3>
      </div> -->

      <div class="w-full mt-4 border rounded-lg">
        <h3 class="flex justify-between items-center text-lg font-bold line-clamp-1 border-b p-6">
          <div>
            <span>待预测数据集</span>
          </div>
        </h3>
        <div class="p-6 pb-0">
          <div
            v-if="dataStatus === 1"
            class="w-full h-[240px] border rounded-lg flex justify-center items-center flex-col mb-6"
          >
            <span class="text-gray-400 text-sm mb-2">数据加载中...</span>
            <Loader2 class="w-8 h-8 animate-spin" />
          </div>

          <div
            v-if="dataStatus === 0"
            class="w-full h-[240px] border rounded-lg flex justify-center items-center flex-col mb-6"
          >
            <span class="text-gray-400 text-sm mb-2">No Data</span>
          </div>
          <div v-for="(item, index) in interList" :key="index" class="border rounded-lg mb-4">
            <div class="line-clamp-1">
              <h3 class="flex justify-between items-center text-lg font-bold line-clamp-1 p-4">
                <div class="w-[80%]">
                  <!-- <span>待预测数据集：</span> -->
                  <span class="text-blue-500">{{ item.name }}</span>
                </div>
                <Badge
                  class="w-[80px] text-center"
                  :class="item.isResult ? 'bg-green-500' : 'bg-gray-300'"
                >
                  {{ item.isResult ? '已完成' : '等待预测' }}
                </Badge>
              </h3>
            </div>
            <div v-if="!item.error" class="p-4 pt-0">
              <div
                class="border rounded-md flex justify-start items-center flex-wrap p-2 pt-2 pb-0"
              >
                <span class="mb-2 text-sm">选择输入圈数：</span>
                <div class="flex justify-start items-center mr-2 mb-2">
                  <ToggleGroup
                    v-model="item.isSelect"
                    type="single"
                    class="flex"
                    :disabled="tasking"
                  >
                    <ToggleGroupItem
                      v-for="opt in radioSelect"
                      :key="opt.value"
                      :value="opt.value"
                      :disabled="opt.disabled"
                      class="h-[32px]"
                    >
                      <span>{{ opt.label }}</span>
                    </ToggleGroupItem>
                  </ToggleGroup>
                </div>
                <div
                  v-if="item.isSelect === '1'"
                  class="text-sm font-medium text-gray-800 dark:text-gray-200 flex justify-between items-center mb-2"
                >
                  <div class="w-[60px] mr-1">
                    <!-- <div class="mb-2 text-center">
                    <span>起始圈数</span>
                  </div> -->
                    <NumberField
                      v-model="item.cycle[0]"
                      class="w-full flex"
                      :min="item.range[0]"
                      :max="item.cycle[1]"
                      @update:model-value="onUpdateCycle(item.cycle, index)"
                    >
                      <NumberFieldInput class="h-[32px]" />
                    </NumberField>
                  </div>
                  <span>~</span>
                  <div class="w-[60px] ml-1">
                    <!-- <div class="mb-2 text-center">
                    <span>终止圈数</span>
                  </div> -->
                    <NumberField
                      v-model="item.cycle[1]"
                      class="w-full flex"
                      :min="item.cycle[0]"
                      :max="item.range[1]"
                      @update:model-value="onUpdateCycle(item.cycle, index)"
                    >
                      <NumberFieldInput class="h-[32px]" />
                    </NumberField>
                  </div>
                  <Popover>
                    <PopoverTrigger as-child>
                      <EllipsisVertical class="w-4 h-4 ml-2 cursor-pointer" />
                      <!-- <div class="flex justify-start items-center text-sm">
                    <span class="mr-1">选择有效圈数：</span>
                    <div
                      class="flex justify-start items-center cursor-pointer border px-2 py-1 rounded-sm hover:bg-gray-100 hover:shadow"
                    >
                      <div>
                        {{ item.cycle[0] }}
                      </div>
                      <div>~</div>
                      <div>
                        {{ item.cycle[1] }}
                      </div>
                    </div>
                  </div> -->
                    </PopoverTrigger>
                    <PopoverContent class="w-[240px]" :align="'end'">
                      <span class="text-sm text-black dark:text-gray-200 line-clamp-1 mb-4">
                        滑动输入
                      </span>
                      <div>
                        <div class="mb-2">
                          <Slider
                            v-model="item.cycle"
                            :min="item.range[0]"
                            :max="item.range[1]"
                            @update:model-value="onUpdateCycle(item.cycle, index)"
                          />
                        </div>
                        <!-- <div
                        class="text-sm font-medium text-gray-800 dark:text-gray-200 flex justify-between items-center mt-2"
                      >
                        <div class="w-1/2 mr-1">
                          <div class="mb-2 text-center">
                            <span>起始圈数</span>
                          </div>
                          <NumberField
                            v-model="item.cycle[0]"
                            class="w-full flex"
                            :min="item.range[0]"
                            :max="item.cycle[1]"
                            @update:model-value="onUpdateCycle(item.cycle, index)"
                          >
                            <NumberFieldInput />
                          </NumberField>
                        </div>
                        <div class="w-1/2 ml-1">
                          <div class="mb-2 text-center">
                            <span>终止圈数</span>
                          </div>
                          <NumberField
                            v-model="item.cycle[1]"
                            class="w-full flex"
                            :min="item.cycle[0]"
                            :max="item.range[1]"
                            @update:model-value="onUpdateCycle(item.cycle, index)"
                          >
                            <NumberFieldInput />
                          </NumberField>
                        </div>
                      </div> -->
                      </div>
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
              <div class="flex justify-between items-center mt-4">
                <span class="text-lg font-bold text-black dark:text-gray-200 line-clamp-1">
                  数据预览
                </span>
              </div>

              <div class="w-full h-[260px] mt-2">
                <EchartLines
                  ref="echartLines"
                  :x-mark-point="item.isSelect === '0' ? item.range : item.cycle"
                />
              </div>
              <div
                v-if="item.predictResult > 0"
                class="border rounded-md p-4 py-2 mt-4 flex justify-between items-center text-base"
              >
                <span>模型预测电池达到寿命阈值圈数:</span>
                <span class="text-red-500">{{ item.predictResult }}</span>
              </div>
            </div>
            <div v-if="item.error" class="mt-4 p-4 pt-0">
              <Alert
                class="bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-300"
              >
                <AlertTitle class="text-yellow-800 dark:text-yellow-300 font-medium">
                  数据集输入提示!
                </AlertTitle>
                <AlertDescription class="text-yellow-700 dark:text-yellow-400">
                  {{ item.error }}
                </AlertDescription>
              </Alert>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { createStudyService } from '@renderer/config/api/grpc/studyService'
import { createTaskService } from '@renderer/config/api/grpc/taskService'
import { getNodeParams, saveNodeParams, updateNodeData } from '@renderer/utils/nodeUtils'
import { EllipsisVertical, Loader2 } from 'lucide-vue-next'
import { nextTick, onMounted, onUnmounted, Ref, ref } from 'vue'
import { toast } from 'vue-sonner'
import EchartLines from './EchartLines.vue'
const props = defineProps({
  nodeData: {
    type: Object,
    required: true,
  },
})
const emit = defineEmits(['update:is-compute'])
const service = createStudyService() // 请求服务
const taskService = createTaskService() // 请求服务
// const taskStore = useTaskStore()

onMounted(() => {
  initService()
})
const eloParams: any = {
  modelFile: '',
  modelId: '',
  inputDatas: [],
}
// const isLoading: Ref<boolean> = ref(false)
const dataStatus: Ref<0 | 1 | 2> = ref(0) // 0: 无数据，1：加载中，2：有数据
const initService = () => {
  dataStatus.value = 0
  // const params = getNodeParams(props.nodeData)
  // isLoading.value = true
  getNodeData()
}
// 检查节点的数据输入
const getNodeData = () => {
  const params = getNodeParams(props.nodeData)
  const input = params['TrainInputData']
  const model = params['TrainModelConfig']
  const inputStatus = {
    input: false,
    model: false,
  }
  if (input) {
    const inputObj = JSON.parse(input.input_datas || '[]')
    const inputs: any = []
    for (let i = 0; i < inputObj.length; i++) {
      const item = inputObj[i]
      inputs.push({
        file_path: item.file_path,
      })
    }
    if (inputs.length > 0) {
      const param = JSON.stringify(inputs)
      checkInputBatteryData(param)
      inputStatus.input = true
    } else {
      dataStatus.value = 0
    }
  }
  if (model) {
    // const data = model.modelResult
    const path = model.modelPath
    // const modelObj = JSON.parse(data || '{}')
    eloParams.modelFile = path
    // eloParams.modelId = modelObj.model_id
    if (eloParams.modelFile) {
      inputStatus.model = true
    }
  }
  if (inputStatus.model && inputStatus.input) {
    const proTaskId = params['proTaskId']
    if (proTaskId) {
      startSubmitLoop(proTaskId)
    }
  }
}
const checkInputBatteryData = async (inputs: any) => {
  interList.value = []
  dataStatus.value = 1
  const inputsRes = await service.checkInputBatteryData(inputs)
  if (Number(inputsRes.statusCode) === 200) {
    const taskId = inputsRes.taskId
    startLoopInputRes(taskId)
  } else {
    dataStatus.value = 0
  }
}
/**
 * 开始推测
 */
const tasking: Ref<boolean> = ref(false)
const eolPredict = async () => {
  tasking.value = true
  if (!eloParams.modelFile) {
    toast.info('操作提示！', {
      description: `请确认是否已经上传了模型！`,
    })
    tasking.value = false
    emit('update:is-compute', false)
    return
  }
  const inputDatas: any = []
  for (let i = 0; i < interList.value.length; i++) {
    console.log(interList.value[i])
    const inter = interList.value[i]
    const item = {
      file_path: inter.path,
      // data_save_dir: inter.dataSaveDir,
      start_cycle: inter.cycle[0],
      end_cycle: inter.cycle[1],
    }
    if (item.end_cycle - item.start_cycle < 20) {
      toast.info('操作提示！', {
        description: `圈数范围过小，请重新选择！至少20圈！`,
      })
      tasking.value = false
      emit('update:is-compute', false)
      return
    }
    inputDatas.push(item)
  }
  if (interList.value.length <= 0) {
    toast.info('操作提示！', {
      description: `请确认是否已经上传了数据集！`,
    })
    tasking.value = false
    emit('update:is-compute', false)
    return
  }
  interList.value.map((m) => (m.isResult = false))
  eloParams.inputDatas = JSON.stringify(inputDatas)
  const res = await service.eolPredict(eloParams.modelFile, eloParams.inputDatas)
  if (res.taskId) {
    startSubmitLoop(res.taskId)
    saveTaskId(res.taskId)
  }
}

let itrTimerTask: any = null
const interList: Ref<any> = ref([])
const startLoopInputRes = (id: any) => {
  if (itrTimerTask) {
    clearInterval(itrTimerTask)
  }
  handleItrTaskRes(id).then()
  itrTimerTask = setInterval(async () => {
    await handleItrTaskRes(id)
  }, 300)
}
const getNameByPath = (path: string) => {
  let result = path.substring(path.indexOf('/') + 1)
  const nameArr = result.split('_')
  const nameFrist = nameArr[0]
  if (nameFrist.length < 10) {
    result = result.substring(result.indexOf('_') + 1)
  }
  return result.substring(result.indexOf('_') + 1)
}
const echartLines: Ref<any> = ref(null)
const handleItrTaskRes = async (id: any) => {
  const res = await taskService.getTaskResult(id)
  if (res.result) {
    endLoopTashRes()
    interList.value = []
    const ret = JSON.parse(res.result || '[]')
    // const dataSaveDir = ret.data_save_dir || ''
    const list = ret || []
    const echartDataArr: any = []
    for (let i = 0; i < list.length; i++) {
      const name: string = getNameByPath(list[i].file_path)
      if (list[i].error) {
        interList.value.push({
          name: name,
          path: list[i].file_path,
          data: list[i].cycle_capacity_array,
          isSelect: '0', // 是否选择自定义
          range: [-1, -1],
          fristValue: null,
          // dataSaveDir,
          cycle: [-1, -1],
          isResult: false,
          // point: [],
          predictResult: 0,
          error: list[i].error,
        })
      } else {
        const data = list[i].cycle_capacity_array || []
        const min = data[0][0] || 0
        const max = data[data.length - 1][0] || 0
        // let fristValue = null // 第一圈容量值

        let fristValue = null // 第一圈容量值
        // if (list[i].cycle_capacity_array && list[i].cycle_capacity_array[0]) {
        //   fristValue = list[i].cycle_capacity_array[0][1]
        // }
        // const xData: any = []
        // const valueArr: any = []
        // const dataArr: any = []
        const valueArr: any = []
        data.forEach((m) => {
          if (fristValue === null) {
            fristValue = m[1]
          }
          let newY: any = 0
          if (fristValue) {
            newY = Math.round((m[1] / fristValue) * 100)
          }
          // dataArr.push([m[0], newY])
          valueArr.push([m[0], m[1], newY])
        })
        // data.map((m) => {
        //   if (fristValue === null) {
        //     fristValue = m[1]
        //   }
        //   if (fristValue) {
        //     m[1] = Math.round((m[1] / fristValue) * 100)
        //   }
        //   return m
        // })
        echartDataArr.push({
          value: valueArr,
        })
        interList.value.push({
          name: name, // 名称
          path: list[i].file_path, // 路径
          range: [min, max], // 圈数范围
          fristValue: fristValue,
          // dataSaveDir, // 保存的路径
          echartIndex: echartDataArr.length - 1, // echarts节点对应的索引
          cycle: [min, max], // 默认圈数
          isSelect: '0', // 是否选择自定义
          isResult: false, // 是否完成预测
          predictResult: 0,
          // point: [], // 预测结果点
          error: false, // 错误提示
        })
      }
    }
    nextTick(() => {
      for (let i = 0; i < echartDataArr.length; i++) {
        const echartDataItem = echartDataArr[i]
        if (echartLines.value) {
          const echartItem = echartLines.value[i]
          if (echartItem) {
            echartItem.setChartData(echartDataItem)
          }
        }
      }
    })
    // setTimeout(() => {
    //   console.log(echartLines.value)
    // }, 10)
    dataStatus.value = 2
    // 将推测结果添加到列表中
    doneResToList()
  }
}
const endLoopTashRes = () => {
  if (itrTimerTask) {
    clearInterval(itrTimerTask)
  }
}
let intervalTimer: any = null
const taskProgress = ref(0)
const startSubmitLoop = (id: any) => {
  // 更新节点数据
  updateNodeData(props.nodeData, {
    taskId: id,
    taskInputData: null,
    taskOutputData: null,
  })
  if (intervalTimer) {
    clearInterval(intervalTimer)
  }
  handleSubmitRes(id).then()
  intervalTimer = setInterval(async () => {
    await handleSubmitRes(id)
  }, 1000)
}
const endLoop = () => {
  if (intervalTimer) {
    clearInterval(intervalTimer)
  }
}
const handleSubmitRes = async (id: any) => {
  const res = await taskService.getTaskList(id)
  const taskList = res.taskResult.taskList
  if (taskList.length > 0) {
    const task = taskList[0]
    taskProgress.value = Math.round(task.taskProcess * 100) / 100
    // Initializing: '初始化中',
    //   Computing: '计算中',
    //   Pending: '等待调度',
    //   Paused: '暂停',
    //   Finished: '任务完成',
    //   Error: '任务失败',
    //   TaskStay: '原始状态',
    //   Abort: '已终止',
    if (
      task.taskStatus === 'Error' ||
      task.taskStatus === 'Abort' ||
      task.taskStatus === 'Paused'
    ) {
      endLoop()
      tasking.value = false
      emit('update:is-compute', false)
      return
    } else if (task.taskStatus === 'Finished') {
      getTaskResult(id)
      endLoop()
    }
  }
}
let taskList: any = []
const getTaskResult = async (id: any) => {
  const res = await taskService.getTaskResult(id)
  const ret = JSON.parse(res.result)
  const retList = ret?.infer_results || []
  taskList = retList
  doneResToList()
  tasking.value = false
  emit('update:is-compute', true)
}
// 将推测结果添加到列表中
const doneResToList = () => {
  if (taskList.length > 0 && interList.value.length > 0) {
    for (let i = 0; i < taskList.length; i++) {
      const findIndex = interList.value.findIndex(
        (item: any) => item.path === taskList[i].file_path,
      )
      if (findIndex !== -1) {
        interList.value[findIndex].isResult = true
        const resNum = taskList[i].predict_result.mean_predicted_eol || 0
        // interList.value[findIndex].data = [...interList.value[findIndex].data, [resNum, 80]]
        // console.log(findIndex)
        nextTick(() => {
          const idx = interList.value[findIndex].echartIndex
          const fristValue = interList.value[findIndex].fristValue
          if (fristValue) {
            const badValue = Math.round(fristValue * 0.8 * 100) / 100
            echartLines.value[idx].setChartMarkPoint([resNum, badValue])
          }
        })
        // interList.value[findIndex].point = [resNum, 80]
        interList.value[findIndex].predictResult = resNum
        interList.value[findIndex].cycle = [taskList[i].start_cycle, taskList[i].end_cycle]
      }
    }
  }
}
const saveTaskId = (id: any) => {
  saveNodeParams(props.nodeData, {
    proTaskId: id,
  })
}
const radioSelect: Ref<any> = ref([
  { label: '全部', value: '0', disabled: false },
  { label: '自定义', value: '1', disabled: false },
])
const onUpdateCycle = (cycle: any, index: any) => {
  interList.value[index].cycle = [...[], ...cycle]
}
const uploadInputData = () => {
  getNodeData()
}
defineExpose({
  eolPredict,
  uploadInputData,
})
onUnmounted(() => {
  endLoop()
  endLoopTashRes()
})
</script>
<style></style>
