<template>
  <Card class="my-4 shadow-sm">
    <CardHeader class="py-2">
      <!-- 使用 flex 布局，在大屏幕上左右分布，小屏幕上垂直排列 -->
      <div class="flex flex-col gap-3 xl:flex-row xl:items-center xl:justify-between">
        <!-- 左侧：CardTitle 和 Dropdown Menu 选择器 -->
        <div class="flex items-center gap-3 flex-wrap sm:flex-nowrap">
          <CardTitle class="text-base font-semibold flex-shrink-0">数据集划分</CardTitle>

          <!-- Dropdown Menu 选择器 -->
          <DropdownMenu>
            <DropdownMenuTrigger as-child>
              <Button variant="outline" size="sm" class="text-xs flex-shrink-0">
                <span>{{ getSelectedTabsLabel() }}</span>
                <LucideIcon name="ChevronDown" class="w-3 h-3 ml-1" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent class="w-48">
              <DropdownMenuCheckboxItem
                v-for="tab in tabs"
                :key="tab.key"
                :checked="selectedTabs.includes(tab.key)"
                @update:checked="(checked) => handleTabSelection(tab.key, checked)"
              >
                <div class="flex items-center justify-between w-full">
                  <span>{{ tab.label }}</span>
                  <Badge variant="secondary" class="text-xs">
                    {{ getSelectedCount(tab.key) }}/{{ getTotalCount(tab.key) }}
                  </Badge>
                </div>
              </DropdownMenuCheckboxItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <!-- 右侧：操作按钮组 -->
        <div class="flex gap-2 w-full sm:w-auto sm:flex-shrink-0">
          <Button
            size="sm"
            variant="outline"
            :disabled="isProcessing"
            class="text-xs flex-1 sm:flex-initial whitespace-nowrap"
            @click="useRecommendedDataset"
          >
            <LucideIcon name="Sparkles" class="w-3 h-3 mr-1 text-blue-500" />
            <span class="truncate">自动划分数据集</span>
          </Button>
          <Button
            size="sm"
            variant="outline"
            :disabled="isProcessing || !hasAnyData"
            class="text-xs flex-1 sm:flex-initial whitespace-nowrap"
            @click="openCustomDatasetDialog"
          >
            <LucideIcon name="Settings" class="w-3 h-3 mr-1 text-neutral-500" />
            <span class="truncate">自定义数据集</span>
          </Button>
        </div>
      </div>
    </CardHeader>

    <CardContent class="py-2 relative">
      <!-- 加载蒙层 -->
      <div
        v-if="isLoadingData"
        class="absolute inset-0 bg-white/80 backdrop-blur-sm z-10 flex items-center justify-center"
      >
        <div class="flex flex-col items-center justify-center space-y-3">
          <div class="animate-spin">
            <LucideIcon name="Loader2" class="w-8 h-8 text-blue-500" />
          </div>
          <p class="text-sm text-blue-600 font-medium">正在加载数据集...</p>
          <p class="text-xs text-muted-foreground">请耐心等待数据处理完成</p>
        </div>
      </div>

      <!-- 有数据时显示数据集 -->
      <div v-if="hasAnyData" class="space-y-4">
        <!-- 选中的数据集展示区域 -->
        <div v-for="tabKey in selectedTabs" :key="tabKey" class="mb-4 last:mb-0">
          <Collapsible :default-open="true">
            <DatasetTabContent
              :tab-key="tabKey"
              :tab-label="getTabLabel(tabKey)"
              :items="getTabItems(tabKey)"
              :is-processing="isProcessing"
              :is-collapsible="true"
              @update:selected-item="handleUpdateSelectedItem"
              @select-all="handleSelectAll"
              @chart-click="handleChartClick"
            />
          </Collapsible>
        </div>

        <!-- 没有选中任何数据集时的提示 -->
        <div v-if="selectedTabs.length === 0" class="text-center py-8 text-muted-foreground">
          <div class="flex flex-col items-center justify-center space-y-2">
            <LucideIcon name="Database" class="w-8 h-8 text-muted-foreground/50" />
            <p class="text-sm">请从上方下拉菜单选择要显示的数据集</p>
          </div>
        </div>
      </div>

      <!-- 无数据状态 -->
      <div v-else class="text-center py-16 text-muted-foreground">
        <div class="flex flex-col items-center justify-center space-y-2">
          <LucideIcon name="Database" class="w-12 h-12 text-muted-foreground/50" />
          <p class="text-sm font-medium">暂无数据集</p>
          <p class="text-xs text-muted-foreground">等待数据加载完成后将自动显示</p>
        </div>
      </div>
    </CardContent>
  </Card>

  <!-- 图表弹框 -->
  <DatasetChartDialog
    v-model:open="showChartDialog"
    :dataset="selectedDataset"
    @update-chart-settings="handleChartSettingsUpdate"
  />
</template>

<script setup lang="ts">
import { LucideIcon } from '@renderer/components'
import DatasetTabContent from './DatasetTabContent.vue'
import DatasetChartDialog from './DatasetChartDialog.vue'
import { useDatasetSplit } from '../composables/useDatasetSplit'

// 定义数据集项的类型
interface DatasetItem {
  id: string
  name: string
  type: 'train' | 'test' | 'val' | 'support' | 'all'
  selected: boolean
  isRecommended?: boolean
  rawData?: {
    file_path: string
    cycle_capacity_array: [number, number][]
    error: any
  }
}

// 定义数据集数据结构
interface DatasetData {
  allData: DatasetItem[]
  trainData: DatasetItem[]
  testData: DatasetItem[]
  valData: DatasetItem[]
  supportData: DatasetItem[]
}

// 定义props
const props = defineProps<{
  isProcessing?: boolean
  isLoadingData?: boolean
}>()

// 使用 defineModel 来处理数据集数据
const datasetData = defineModel<DatasetData>('datasetData', {
  default: () => ({
    allData: [],
    trainData: [],
    testData: [],
    valData: [],
    supportData: [],
  }),
})

// 定义emit
const emit = defineEmits<{
  'use-recommended-dataset': []
  'open-custom-dataset': []
  'update-chart-settings': [itemId: string, startCycle: number, endCycle: number]
}>()

// 使用组合式函数
const {
  showChartDialog,
  selectedDataset,
  selectedTabs,
  tabs,
  hasAnyData,
  getTabItems,
  getTotalCount,
  getSelectedCount,
  getTabLabel,
  getSelectedTabsLabel,
  handleTabSelection,
  handleUpdateSelectedItem,
  handleUpdateSelectedItems,
  handleSelectAll,
  handleChartClick,
} = useDatasetSplit(datasetData)

// 使用推荐数据集
const useRecommendedDataset = () => {
  emit('use-recommended-dataset')
}

// 打开自定义数据集对话框
const openCustomDatasetDialog = () => {
  emit('open-custom-dataset')
}

// 处理图表设置更新
const handleChartSettingsUpdate = (itemId: string, startCycle: number, endCycle: number) => {
  console.log('DatasetSplit - 图表设置更新:', { itemId, startCycle, endCycle })
  emit('update-chart-settings', itemId, startCycle, endCycle)
}
</script>

<style scoped></style>
