import { useFlowsStore } from '@renderer/store'

/**
 * 获取节点参数
 * @param nodeData 节点数据
 * @returns 节点参数
 */
export function getNodeParams(nodeData) {
  const flowsStore = useFlowsStore()
  const { id: nodeId, data } = nodeData
  const workflowId = data?.workflowId

  if (!workflowId || !nodeId) return {}

  return flowsStore.getNodeParams(workflowId, nodeId) || {}
}

/**
 * 保存节点参数
 * @param nodeData 节点数据
 * @param params 要保存的参数
 */
export function saveNodeParams(nodeData, params) {
  const flowsStore = useFlowsStore()
  const { id: nodeId, data } = nodeData
  const workflowId = data?.workflowId

  if (!workflowId || !nodeId) return false

  // 保存到 store
  flowsStore.saveNodeParams(workflowId, nodeId, params)

  // 更新节点数据
  if (nodeData.data) {
    nodeData.data.params = params
  }

  return true
}

/**
 * 删除节点参数
 * @param nodeData 节点数据
 * @returns 是否成功删除
 */
export function deleteNodeParams(nodeData) {
  const flowsStore = useFlowsStore()
  const { id: nodeId, data } = nodeData
  const workflowId = data?.workflowId

  if (!workflowId || !nodeId) return false

  // 从 store 中删除
  flowsStore.deleteNodeParams(workflowId, nodeId)

  // 清空节点数据中的参数
  if (nodeData.data) {
    nodeData.data.params = {}
  }

  return true
}

/**
 * 获取节点的输入节点
 * @param nodeData 节点数据
 * @returns 输入节点数组
 */
export function getInputNodes(nodeData) {
  const flowsStore = useFlowsStore()
  const { id: nodeId, data } = nodeData
  const workflowId = data?.workflowId

  if (!workflowId || !nodeId) return []

  const workflow = flowsStore.getWorkflow(workflowId)
  if (!workflow || !workflow.edges || !workflow.nodes) return []

  const inputEdges = workflow.edges.filter((e) => e.target === nodeId)

  return inputEdges
    .map((edge) => {
      const sourceNodeId = edge.source
      const sourceNode = workflow.nodes.find((n) => n.id === sourceNodeId)

      if (!sourceNode) return null

      return {
        node: sourceNode,
        params: flowsStore.getNodeParams(workflowId, sourceNodeId) || {},
        type: sourceNode.data?.type,
      }
    })
    .filter(Boolean)
}

/**
 * 获取节点的输出节点
 * @param nodeData 节点数据
 * @returns 输出节点数组
 */
export function getOutputNodes(nodeData) {
  const flowsStore = useFlowsStore()
  const { id: nodeId, data } = nodeData
  const workflowId = data?.workflowId

  if (!workflowId || !nodeId) return []

  const workflow = flowsStore.getWorkflow(workflowId)
  if (!workflow || !workflow.edges || !workflow.nodes) return []

  const outputEdges = workflow.edges.filter((e) => e.source === nodeId)

  return outputEdges
    .map((edge) => {
      const targetNodeId = edge.target
      const targetNode = workflow.nodes.find((n) => n.id === targetNodeId)

      if (!targetNode) return null

      return {
        node: targetNode,
        params: flowsStore.getNodeParams(workflowId, targetNodeId) || {},
        type: targetNode.data?.type,
      }
    })
    .filter(Boolean)
}

/**
 * 按类型获取输入节点
 * @param nodeData 节点数据
 * @param type 节点类型
 * @returns 指定类型的输入节点
 */
export function getInputNodesByType(nodeData, type) {
  const inputs = getInputNodes(nodeData)
  return inputs.filter((input) => input.type === type)
}

/**
 * 按类型获取输出节点
 * @param nodeData 节点数据
 * @param type 节点类型
 * @returns 指定类型的输出节点
 */
export function getOutputNodesByType(nodeData, type) {
  const outputs = getOutputNodes(nodeData)
  return outputs.filter((output) => output.type === type)
}

/**
 * 检查两个节点是否可以连接
 * @param sourceNode 源节点
 * @param targetNode 目标节点
 * @returns 是否可连接
 */
export function canNodesConnect(sourceNode, targetNode) {
  if (!sourceNode?.data || !targetNode?.data) return false

  const sourceType = sourceNode.data.type
  const targetInputTypes = targetNode.data.inputType || []

  return targetInputTypes.includes(sourceType)
}

/**
 * 更新节点数据
 * @param nodeData 节点数据
 * @param newData 新数据
 */
export function updateNodeData(nodeData, newData) {
  if (!nodeData || !nodeData.data) return false

  // 合并数据
  Object.assign(nodeData.data, newData)

  // 获取工作流ID和节点ID
  const workflowId = nodeData.data.workflowId
  const nodeId = nodeData.id

  // 如果有工作流ID，将更改保存到flows存储
  if (workflowId) {
    const flowsStore = useFlowsStore()
    const workflow = flowsStore.getWorkflow(workflowId)

    if (workflow && workflow.nodes) {
      // 找到节点并更新其数据
      const nodeIndex = workflow.nodes.findIndex((node) => node.id === nodeId)
      if (nodeIndex !== -1) {
        // 更新节点数据
        workflow.nodes[nodeIndex].data = {
          ...workflow.nodes[nodeIndex].data,
          ...newData,
        }

        // 使用现有的saveWorkflow方法保存整个工作流
        flowsStore.saveWorkflow(workflowId, workflow)
      }
    }
  }

  return true
}

/**
 * 获取工作流中的所有节点
 * @param workflowId 工作流ID
 * @returns 节点数组
 */
export function getAllNodes(workflowId) {
  const flowsStore = useFlowsStore()
  const workflow = flowsStore.getWorkflow(workflowId)

  if (!workflow || !workflow.nodes) return []

  return workflow.nodes
}

/**
 * 获取工作流中的所有连接
 * @param workflowId 工作流ID
 * @returns 连接数组
 */
export function getAllEdges(workflowId) {
  const flowsStore = useFlowsStore()
  const workflow = flowsStore.getWorkflow(workflowId)

  if (!workflow || !workflow.edges) return []

  return workflow.edges
}
// 添加节点监听函数
export function addNodeListener(callback) {
  const flowsStore = useFlowsStore()
  flowsStore.listenNodeLink(callback)
}
