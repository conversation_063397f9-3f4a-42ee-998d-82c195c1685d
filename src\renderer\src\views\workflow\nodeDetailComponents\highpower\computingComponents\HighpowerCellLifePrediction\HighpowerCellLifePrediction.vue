<!-- HighpowerCellLifePrediction.vue -->
<template>
  <div class="p-1">
    <div class="pb-4">
      <div class="flex justify-between items-center dark:text-white/80">
        <span class="text-2xl font-bold">模式选择</span>
        <div class="w-1/2">
          <Select
            v-model="modelSelectValue"
            class="w-full"
            :disabled="isSelectDisabled"
            @update:model-value="selectChange"
          >
            <SelectTrigger>
              <SelectValue placeholder="模型选择" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectItem v-for="(item, index) in modelList" :key="index" :value="item.value">
                  {{ item.label }}
                </SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
    <Card>
      <CardHeader>
        <CardTitle>
          <div v-if="!isInference" class="flex justify-between dark:text-white/80">
            <span class="text-2xl font-bold">寿命预测</span>
            <div>
              <ExportButton
                :disabled="
                  !!currentTaskId && ['Computing', 'Pending', 'Initializing'].includes(taskStatus)
                "
                :can-export="canExport"
                @export="handleExport"
              />
              <!-- 任务控制下拉菜单 - 只有在有任务运行或暂停时才显示 -->
              <DropdownMenu v-if="taskControlMenuItems.length > 0">
                <DropdownMenuTrigger as-child>
                  <Button variant="outline" size="sm" class="p-2 ml-2">
                    <LucideIcon name="EllipsisVertical" class="w-4 h-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem
                    v-for="item in taskControlMenuItems"
                    :key="item.key"
                    :class="item.hoverColor"
                    @click="item.handler"
                  >
                    <LucideIcon :name="item.icon" :class="`w-4 h-4 mr-2 ${item.iconColor}`" />
                    <span :class="`${item.textColor} font-medium`">{{ item.label }}</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
          <div v-if="isInference" class="flex justify-between dark:text-white/80">
            <span class="text-2xl font-bold">寿命预测</span>
            <Button
              size="sm"
              class="dark:text-muted-foreground dark:bg-muted-foreground"
              :disabled="tasking"
              @click="handleInference"
            >
              <Loader2 v-if="tasking" class="w-4 h-4 mr-2 animate-spin" />
              <Check v-else class="w-4 h-4 mr-2" />
              <span>开始预测</span>
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent class="space-y-6">
        <!-- <div class="flex justify-end items-center">
          <Button
            class="h-[32px] dark:text-muted-foreground dark:bg-muted-foreground"
            @click="handleInference"
          >
            <Loader2 v-if="tasking" class="w-4 h-4 animate-spin" />
            <Check v-else class="w-4 h-4" />
            <span class="text-xs">寿命预测</span>
          </Button>
        </div> -->
        <div v-if="isInference">
          <TrainInferential
            ref="trainInferential"
            :node-data="nodeData"
            @update:is-compute="handleUpdateIsCompute"
          />
        </div>
        <div v-else>
          <!-- 基本参数配置-->
          <BasicSettings
            :basic-params="basicParams"
            :is-disabled="!!currentTaskId"
            :charge-types="charges"
            @update:basic-params="updateBasicParams"
          />
          <!-- 预测表单 -->
          <PredictionForm
            v-model:cycle="cycle"
            :disabled="
              !!currentTaskId && ['Computing', 'Pending', 'Initializing'].includes(taskStatus)
            "
            @start-prediction="openSubmitDialog"
          />

          <!-- 进度指示器 -->
          <ProgressIndicator
            v-if="currentTaskId"
            :progress-value="progress"
            :current-cycle="currentCycle"
            :total-cycle="cycle"
          />

          <!-- 加载中状态 -->
          <!-- <ChartSkeleton v-if="currentTaskId && !chartsReady" /> -->

          <!-- 空图表状态 -->
          <EmptyCharts
            v-if="!chartsReady || !taskResult"
            :is-loading="
              !!currentTaskId && ['Computing', 'Pending', 'Initializing'].includes(taskStatus)
            "
          />

          <!-- 图表容器 -->
          <Transition name="fade" mode="out-in">
            <ChartContainer
              v-show="chartsReady && taskResult"
              v-model:is-ready="chartsReady"
              :chart-series="chartSeries"
              :result-data="taskResult"
            />
          </Transition>
        </div>
      </CardContent>
      <SubmitTaskDialog
        v-model:is-open="isSubmitDialogOpen"
        service-name="lifePrediction"
        @submit="handleSubmitTask"
      />
    </Card>
  </div>
</template>

<script setup lang="ts">
import { SubmitTaskDialog } from '@renderer/components'
import { createTaskService } from '@renderer/config/api/grpc/taskService'
import { useFlowsStore, useTaskStore } from '@renderer/store'
import {
  addNodeListener,
  getInputNodes,
  getNodeParams,
  saveNodeParams,
  updateNodeData,
} from '@renderer/utils/nodeUtils'
import { inputParse } from '@renderer/utils/rpcParser'
import { useDebounceFn } from '@vueuse/core'
import { Check, Loader2 } from 'lucide-vue-next'
import { computed, onMounted, onUnmounted, Ref, ref, watch } from 'vue'
import { toast } from 'vue-sonner'
import {
  BasicSettings,
  ChartContainer,
  EmptyCharts,
  ExportButton,
  PredictionForm,
  ProgressIndicator,
  TrainInferential,
} from './components'

// 任务操作配置
const taskOperations: Record<
  string,
  {
    name: string
    action: (taskId: string) => Promise<any>
    successMessage: string
    successDescription: string
    errorMessage: string
    warningMessage: string
    warningDescription: string
    requiresProcessing: boolean
    afterSuccess?: (taskId: string) => void
  }
> = {
  pause: {
    name: '暂停',
    action: (taskId: string) => taskService.pauseTask(taskId),
    successMessage: '任务已暂停',
    successDescription: '寿命预测任务已暂停',
    errorMessage: '暂停失败',
    warningMessage: '无法暂停',
    warningDescription: '没有正在进行的任务',
    requiresProcessing: true,
  },
  stop: {
    name: '终止',
    action: (taskId: string) => taskService.stopTask(taskId),
    successMessage: '任务已终止',
    successDescription: '寿命预测任务已终止',
    errorMessage: '终止失败',
    warningMessage: '无法终止',
    warningDescription: '没有可终止的任务',
    requiresProcessing: false,
    afterSuccess: (taskId: string) => taskStore.stopPolling(taskId),
  },
  resume: {
    name: '恢复',
    action: (taskId: string) => taskService.resumeTask(taskId),
    successMessage: '任务已恢复',
    successDescription: '寿命预测任务已恢复',
    errorMessage: '恢复失败',
    warningMessage: '无法恢复',
    warningDescription: '没有可恢复的任务',
    requiresProcessing: false,
    afterSuccess: (taskId: string) => taskStore.startPolling(taskId),
  },
}

// 组件属性
const props = defineProps({
  nodeData: {
    type: Object,
    required: true,
  },
})

// 服务和状态管理
const taskStore = useTaskStore()
const flowsStore = useFlowsStore()
const taskService = createTaskService()

// 状态变量
const isSubmitDialogOpen = ref(false)
const cycle = ref<number | undefined>(undefined)
const currentCycle = ref(0)
const chartsReady = ref(false)
const currentTaskId = ref('')

// 基本参数
const charges = [{ value: 'CP', label: '恒功率充电' }]
const basicParams = ref({
  chargeType: 'CP',
  chargeValue: 896,
})

// =========================计算属性=========================
//获取豪鹏电池模型数据
const batteryModelParams = computed(() => {
  if (props.nodeData?.id && props.nodeData?.data?.workflowId) {
    const params = flowsStore.getNodeParams(props.nodeData.data.workflowId, props.nodeData.id)
    return params?.HighpowerBatteryModel || {}
  }
  return {}
})
// console.log('batteryModelParams====', batteryModelParams.value)
// 获取电池模型相关参数
const modelData = computed(() => {
  const params = batteryModelParams.value
  return {
    elec_params: params.elec_params || '',
    aging_params: params.aging_params || '',
    modelBuilt: params.modelBuilt || false,
    all_params: params.all_params || '',
  }
})

const progress = computed(() => taskStore.getTaskProgress(currentTaskId.value).value)
const taskResult = computed(() => taskStore.getTaskResultById(currentTaskId.value).value)
const taskStatus = computed(() => taskStore.getTaskStatus(currentTaskId.value).value)
const chartSeries = computed(() => {
  if (!taskResult.value) return []
  return Object.keys(taskResult.value).filter((key) => key !== '循环圈数' && key !== 'Cycle number')
})
const canExport = computed(() => !!taskResult.value && chartsReady.value)

// 任务控制菜单配置
const taskControlMenuItems = computed(() => {
  const items: Array<{
    key: string
    label: string
    icon: string
    iconColor: string
    textColor: string
    hoverColor: string
    handler: () => Promise<void>
  }> = []

  // 暂停按钮 - 任务运行时显示
  if (['Computing', 'Pending', 'Initializing'].includes(taskStatus.value)) {
    items.push({
      key: 'pause',
      label: '暂停',
      icon: 'CirclePause',
      iconColor: 'text-orange-500',
      textColor: 'text-orange-600',
      hoverColor: 'hover:bg-orange-50',
      handler: handlePause,
    })
  }

  // 恢复按钮 - 任务暂停时显示
  if (taskStatus.value === 'Paused') {
    items.push({
      key: 'resume',
      label: '恢复',
      icon: 'Play',
      iconColor: 'text-green-500',
      textColor: 'text-green-600',
      hoverColor: 'hover:bg-green-50',
      handler: handleResume,
    })
  }

  // 终止按钮 - 任务运行或暂停时显示
  if (['Computing', 'Pending', 'Initializing', 'Paused'].includes(taskStatus.value)) {
    items.push({
      key: 'stop',
      label: '终止',
      icon: 'Ban',
      iconColor: 'text-red-500',
      textColor: 'text-red-600',
      hoverColor: 'hover:bg-red-50',
      handler: handleStop,
    })
  }

  return items
})

// 更新基本参数
const updateBasicParams = (newParams: { chargeType: string; chargeValue: number }) => {
  basicParams.value = { ...newParams }

  // 保存更新后的基本参数
  const currentParams = getNodeParams(props.nodeData)
  saveNodeParams(props.nodeData, {
    ...currentParams,
    basicParams: JSON.stringify(basicParams.value),
  })
}

// 统一提示处理
const handleToast = (type, title, description) => {
  switch (type) {
    case 'success':
      toast.success(title, { description })
      break
    case 'error':
      toast.error(title, { description })
      break
    case 'warning':
      toast.warning(title, { description })
      break
    case 'info':
      toast.info(title, { description })
      break
  }
}

// 通用任务操作处理函数
const handleTaskOperation = async (operationType: keyof typeof taskOperations) => {
  const operation = taskOperations[operationType]

  // 检查是否需要任务正在处理中
  if (
    operation.requiresProcessing &&
    !['Computing', 'Pending', 'Initializing'].includes(taskStatus.value)
  ) {
    return
  }

  if (!currentTaskId.value) {
    handleToast('warning', operation.warningMessage, operation.warningDescription)
    return
  }

  try {
    console.log(`开始${operation.name}任务:`, currentTaskId.value)
    const result = await operation.action(currentTaskId.value)

    if (result.status === 'Success') {
      await taskStore.updateTaskList(currentTaskId.value)

      // 执行成功后的额外操作
      if (operation.afterSuccess) {
        operation.afterSuccess(currentTaskId.value)
      }

      handleToast('success', operation.successMessage, operation.successDescription)
      console.log(`${operation.name}任务成功:`, currentTaskId.value)
    } else {
      handleToast(
        'error',
        operation.errorMessage,
        result.message || `${operation.name}任务时发生错误`,
      )
    }
  } catch (error: any) {
    console.error(`${operation.name}任务失败:`, error)
    handleToast('error', operation.errorMessage, error.message || `${operation.name}任务时发生错误`)
  }
}

// 具体的处理函数
const handlePause = () => handleTaskOperation('pause')
const handleStop = () => handleTaskOperation('stop')
const handleResume = () => handleTaskOperation('resume')

// 打开提交任务弹框
const openSubmitDialog = () => {
  isSubmitDialogOpen.value = true
}

// 处理任务提交
const handleSubmitTask = useDebounceFn(async (formData) => {
  try {
    if (!modelData.value.elec_params || !modelData.value.aging_params) {
      handleToast('error', '参数缺失', '电池参数或老化参数不存在，请先上传电池模型')
      return
    }

    if (!modelData.value.modelBuilt) {
      handleToast('warning', '电池模型暂未构建', '请先在豪鹏电池模型中构建电池模型')
      return
    }

    if (!cycle.value || cycle.value <= 0) {
      handleToast('warning', '无效参数', '请输入有效的预测圈数')
      return
    }

    // 重置状态
    chartsReady.value = false
    currentCycle.value = 0

    const taskParams = {
      ...formData,
      cycle: String(cycle.value),
      power: '0.5',
      all_params: modelData.value.all_params || '',
      elec_params: modelData.value.elec_params,
      aging_params: modelData.value.aging_params,
    }

    const keyValuePairs = {
      cycle: taskParams.cycle,
      power: taskParams.power,
      all_params: taskParams.all_params,
      elec_params: taskParams.elec_params,
      aging_params: taskParams.aging_params,
      charge_type: basicParams.value.chargeType,
      charge_value: basicParams.value.chargeValue,
    }

    const keyTypePairs = {
      cycle: 'Int32',
      power: 'Float',
      all_params: 'String',
      elec_params: 'Jsonarray',
      aging_params: 'Jsonarray',
      charge_type: 'String',
      charge_value: 'String',
    }

    const paramData = inputParse(keyValuePairs, keyTypePairs)
    const res = await taskService.submitTask(
      taskParams.service_name,
      taskParams.server_id,
      paramData,
    )

    if (res.status === 'Success') {
      currentTaskId.value = res.taskId

      // 保存节点参数
      saveNodeParams(props.nodeData, {
        taskId: res.taskId,
        cycle: cycle.value,
        basicParams: JSON.stringify(basicParams.value),
      })

      // 更新节点数据
      updateNodeData(props.nodeData, {
        taskId: res.taskId,
        taskInputData: keyValuePairs,
        taskOutputData: null,
      })

      // 开始轮询任务状态
      taskStore.startPolling(res.taskId)
      handleToast('success', '提交成功', '任务已提交')
    } else {
      throw new Error(res.message)
    }
  } catch (error) {
    handleToast('error', '提交任务失败', error)
  }
}, 300)

// 导出处理函数
const handleExport = useDebounceFn(async () => {
  try {
    if (!taskResult.value || Object.keys(taskResult.value).length === 0) {
      handleToast('info', '提示', '没有可导出的数据')
      return
    }

    const dateStr = new Date().toISOString().replace(/[:.]/g, '-').substring(0, 19)
    const defaultFileName = `寿命预测结果_${dateStr}`
    const filters = [
      { name: 'CSV文件', extensions: ['csv'] },
      { name: 'JSON文件', extensions: ['json'] },
      { name: '所有文件', extensions: ['*'] },
    ]

    const saveDialogResult = await window.electronAPI.saveFile({
      title: '保存预测结果',
      defaultPath: defaultFileName,
      filters: filters,
    })

    if (!saveDialogResult.success || saveDialogResult.canceled) {
      handleToast('info', '提示', '导出取消')
      return
    }

    const filePath = saveDialogResult.filePath
    const fileExt = filePath.substring(filePath.lastIndexOf('.')).toLowerCase()
    let content = ''

    if (fileExt === '.json') {
      content = JSON.stringify(taskResult.value, null, 2)
    } else {
      content = '\uFEFF' + convertToCSV(taskResult.value)
    }

    const result = await window.electronAPI.saveFile({
      filePath: filePath,
      content: content,
      encoding: 'utf8',
    })

    if (result.success) {
      handleToast('success', '导出成功', `文件已保存至: ${result.filePath}`)
    } else {
      handleToast('error', '导出失败', result.error || '未知错误')
    }
  } catch (error) {
    handleToast('error', '导出结果失败', error)
  }
}, 300)

// 转换数据为CSV格式
const convertToCSV = (data) => {
  if (!data) return ''

  const cycleKey = data['Cycle number'] ? 'Cycle number' : data['循环圈数'] ? '循环圈数' : null
  if (!cycleKey) return ''

  const keys = Object.keys(data)
  const orderedKeys = [cycleKey, ...keys.filter((key) => key !== cycleKey)]
  let csv = orderedKeys.join(',') + '\n'

  const dataLength = data[cycleKey].length
  for (let i = 0; i < dataLength; i++) {
    const row = orderedKeys.map((key) => {
      if (data[key] && data[key][i] !== undefined) {
        const cellValue = String(data[key][i])
        return cellValue.includes(',') ? `"${cellValue}"` : cellValue
      }
      return ''
    })
    csv += row.join(',') + '\n'
  }

  return csv
}

// 加载节点参数
const loadNodeParams = () => {
  const params = getNodeParams(props.nodeData)
  const inputNodes = getInputNodes(props.nodeData)
  if (inputNodes.length > 0) {
    isSelectDisabled.value = true
  } else {
    isSelectDisabled.value = false
  }
  modelSelectValue.value = '0'
  const inputTypes = ['TrainInputData', 'TrainModelConfig']
  for (let i = 0; i < inputNodes.length; i++) {
    const inputNode = inputNodes[i]
    if (inputTypes.includes(inputNode.type)) {
      // 处理 TrainInputData 和 TrainModelConfig 的参数
      isInference.value = true
      modelSelectValue.value = '1'
      return
    }
  }
  if (params) {
    cycle.value = params.cycle || null
    currentTaskId.value = params.taskId || props.nodeData.data?.taskId
    currentCycle.value = params.currentCycle || 0

    // 加载基本参数
    if (params.basicParams) {
      // console.log('加载基本参数====', params.basicParams)
      basicParams.value =
        typeof params.basicParams === 'string' ? JSON.parse(params.basicParams) : params.basicParams
    }
  }
}

// 监听任务状态变化
watch(
  () => (currentTaskId.value ? taskStore.getTaskStatus(currentTaskId.value).value : null),
  (newStatus) => {
    if (!currentTaskId.value) return

    // 如果任务状态变为已完成，获取最终结果
    if (newStatus && ['Finished', 'Error', 'Abort'].includes(newStatus)) {
      taskStore.updateTaskResult(currentTaskId.value)
      // 任务完成后重置任务ID
      // currentTaskId.value = ''
    }
  },
)

// 监听任务结果变化
watch(
  () => (currentTaskId.value ? taskStore.getTaskResultById(currentTaskId.value).value : null),
  (newResult) => {
    if (newResult) {
      const cycleData = newResult['循环圈数'] || newResult['Cycle number']
      if (cycleData && Array.isArray(cycleData)) {
        currentCycle.value = cycleData.length
      }
    }
  },
)

// 组件挂载时
onMounted(() => {
  // 加载节点参数
  loadNodeParams()

  // 如果有任务ID，更新任务结果
  if (currentTaskId.value) {
    taskStore.updateTaskResult(currentTaskId.value)

    // 根据任务状态决定是否启动轮询
    const status = taskStore.getTaskStatus(currentTaskId.value).value
    if (['Computing', 'Pending', 'Initializing'].includes(status)) {
      taskStore.startPolling(currentTaskId.value)
    }
  }
})

// 新增推理寿命预测-------------------------------->>>>>
addNodeListener(({ workflowId, sourceNode }) => {
  if (sourceNode) {
    isSelectDisabled.value = true
  } else {
    isSelectDisabled.value = false
  }
  if (workflowId === props.nodeData.data.workflowId) {
    const source = sourceNode.data.type
    if (source === 'TrainInputData' || source === 'TrainModelConfig') {
      isInference.value = true
      modelSelectValue.value = '1'
      if (trainInferential.value) {
        trainInferential.value.uploadInputData()
      }
    } else {
      isInference.value = false
      modelSelectValue.value = '0'
    }
  }
})
const isInference = ref(false)
const trainInferential: any = ref(null)
const handleInference = () => {
  if (tasking.value) {
    return
  }
  if (trainInferential.value) {
    tasking.value = true
    trainInferential.value.eolPredict()
  }
}
const tasking = ref(false) // 任务进行中
const handleUpdateIsCompute = () => {
  tasking.value = false
}
const modelList: Ref<any> = ref([
  { label: '机理模型预测', value: '0' },
  { label: '机器学习终点预测', value: '1' },
])
const modelSelectValue: Ref<any> = ref('0')
const isSelectDisabled: Ref<any> = ref(false)
const selectChange = (value: any) => {
  if (value === '0') {
    isInference.value = false
  } else {
    isInference.value = true
  }
}
// 组件卸载时
onUnmounted(() => {
  // 停止任务轮询
  if (currentTaskId.value) {
    taskStore.stopPolling(currentTaskId.value)
  }
})
</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
