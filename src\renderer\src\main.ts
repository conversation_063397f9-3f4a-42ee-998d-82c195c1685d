// 引入全局样式
import '@renderer/config/styles/index.scss'

import { createApp } from 'vue'
import App from './App.vue'
//路由
import router from './router'
//状态管理
import { createPinia } from 'pinia'
// import piniaPluginPersist from 'pinia-plugin-persist'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
//指令
import ChartResizeDirectives from '@renderer/config/directives/VChartResize'
import tooltip from '@renderer/config/directives/VTooltip'
// 导入全局组件插件
import globalComponents from '@renderer/config/plugins/globalComponents'
import i18n from './locales'

import { useSettingsStore } from '@renderer/store'

const app = createApp(App)

// 创建并挂载 pinia
const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)
app.use(pinia)

// 挂载路由
app.use(router)
// 注册全局组件插件
app.use(globalComponents)
// 挂载指令
app.directive('tooltip', tooltip)
app.use(ChartResizeDirectives)
app.use(i18n)

// 挂载应用
app.mount('#app')

// 在应用挂载后初始化设置 store
const settingsStore = useSettingsStore()
settingsStore.initializeSettings()
